package com.heal.riverbed.constants;

public class Constants {
    public static final String RIVERBED_KPI_EXTRACTOR_CONFIGURATION_ERROR = "RiverbedKpiExtractorConfigurationError";
    public static final String RIVERBED_KPI_EXTRACTOR_ERRORS = "RiverbedKpiExtractorErrors";
    public static final String RIVERBED_KPI_EXTRACTOR_INITIALIZATION_ERROR = "RiverbedKpiExtractorInitializationError";

    public static final String RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR = "RiverbedForensicsExtractorConfigurationError";
    public static final String RIVERBED_FORENSICS_EXTRACTOR_ERRORS = "RiverbedForensicsExtractorErrors";
    public static final String RIVERBED_FORENSICS_EXTRACTOR_INITIALIZATION_ERROR = "RiverbedForensicsExtractorInitializationError";

    public static final String RIVERBED_URL = "riverbed.url";
    public static final String RIVERBED_KPI_REQUEST = "riverbed.request.payload";
    public static final String RIVERBED_FORENSICS_REQUEST = "riverbed.forensics.request.payload";
    public static final String RIVERBED_AUTHORIZATION = "riverbed.authorization";
    public static final String RIVERBED_ENTITY_TYPES = "riverbed.entity.types";

}
