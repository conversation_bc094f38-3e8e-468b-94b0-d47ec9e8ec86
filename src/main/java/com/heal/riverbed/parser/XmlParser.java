package com.heal.riverbed.parser;

import com.heal.riverbed.pojos.*;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Unmarshaller;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.StringReader;
import java.util.*;

@Slf4j
@Service
public class XmlParser {

    public Map<String, Map<String, Double>> buildKpiRecords(String xmlResponse, int attributeIndex, Map<Integer, String> columnMap,
                                                            String instanceIdentifier) {
        log.trace("Inside buildKpiRecords(instanceIdentifier:{}, attributeIndex:{}, columnMap:{}) method", instanceIdentifier, attributeIndex, columnMap);
        Map<String, Map<String, Double>> kpiDataMap = new HashMap<>();
        if (xmlResponse == null || xmlResponse.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(Report.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            Report report = (Report) unmarshaller.unmarshal(new StringReader(xmlResponse));

            if (report.getQueries() == null || report.getQueries().isEmpty()) {
                log.error("No queries output in response for instanceIdentifier:{}", instanceIdentifier);
                return Collections.emptyMap();
            }

            log.info("Queries size:{} for instanceIdentifier:{}", report.getQueries().size(), instanceIdentifier);
            List<Row> rows = report.getQueries().get(0).getData().getRows();
            if (rows == null || rows.isEmpty()) {
                log.error("No rows output in response for instanceIdentifier:{}", instanceIdentifier);
                return Collections.emptyMap();
            }

            log.info("Number of rows:{} for instanceIdentifier:{}", rows.size(), instanceIdentifier);
            for (Row row : rows) {
                List<String> values = row.getValues();
                if (values == null || values.isEmpty()) {
                    continue;
                }

                String attribute = values.get(attributeIndex);

                Map<String, Double> kpiMap = new HashMap<>();
                columnMap.forEach((index, kpiName) -> {
                    if (index < 0 || index >= values.size()) {
                        log.error("Index does not exists to extract kpi value for attribute:{}, kpiName:{}, index:{}",
                                attribute, kpiName, index);
                        return;
                    }

                    String kpiValueStr = values.get(index);
                    if (kpiValueStr == null) {
                        log.error("Kpi value does not exists for attribute:{}, kpiName:{}, index:{}",
                                attribute, kpiName, index);
                        return;
                    }

                    Double kpiValue = parseDoubleSafe(kpiValueStr);
                    if (kpiValue == null) {
                        log.error("Could not convert the value to double for attribute:{}, kpiName:{}, value:{}",
                                attribute, kpiName, kpiValueStr);
                        return;
                    }
                    kpiMap.put(kpiName, kpiValue);
                });
                kpiDataMap.put(attribute, kpiMap);
            }
        } catch (Exception e) {
            log.error("Error occurred while processing the xml response to kpi data for instanceIdentifier:{}", instanceIdentifier, e);
        }
        return kpiDataMap;
    }

    private Double parseDoubleSafe(String text) {
        try {
            return Double.valueOf(text);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public Map<String, Map<String, Double>> pivotMap(Map<String, Map<String, Double>> kpiDataMap, String srcInstIdentifier) {
        log.trace("Inside pivotMap(srcInstIdentifier:{}) method", srcInstIdentifier);
        Map<String, Map<String, Double>> kpiToAttributes = new HashMap<>();
        for (Map.Entry<String, Map<String, Double>> attributeEntry : kpiDataMap.entrySet()) {
            String attribute = attributeEntry.getKey();
            Map<String, Double> kpis = attributeEntry.getValue();

            for (Map.Entry<String, Double> kpiEntry : kpis.entrySet()) {
                String kpi = kpiEntry.getKey();
                Double value = kpiEntry.getValue();

                kpiToAttributes
                        .computeIfAbsent(kpi, k -> new HashMap<>())
                        .put(attribute, value);
            }
        }

        return kpiToAttributes;
    }

    public Map<Integer, String> getKpiColumnIndex(String scrInstIdentifier, String parameter) {
        Map<Integer, String> resultMap = new HashMap<>();
        // Split by comma
        String[] entries = parameter.split(",");

        for (String entry : entries) {
            String[] parts = entry.trim().split("#");
            if (parts.length == 2) {
                try {
                    Integer key = Integer.valueOf(parts[0].trim());
                    String value = parts[1].trim();
                    resultMap.put(key, value);
                } catch (NumberFormatException e) {
                    log.error("Error occurred while converting into parameter:{}, scrInstIdentifier:{}", parameter, scrInstIdentifier);
                }
            }
        }

        return resultMap;
    }

    public String prepareRequestPayload(String entity, String columns, String inf, String gpb, String limit, String resolution, long from, long to, String template) {
        Date startDate = new Date(from);
        Date endDate = new Date(to);
        Map<String, Object> placeholders = Map.of(
                "${startTime}", startDate.toInstant().getEpochSecond(),
                "${endTime}", endDate.toInstant().getEpochSecond(),
                "${resolution}", resolution,
                "${branchIp}", entity,
                "${interface}", inf,
                "${groupBy}", gpb,
                "${limit}", limit,
                "${columns}", columns
        );
        for (Map.Entry<String, Object> entry : placeholders.entrySet()) {
            template = template.replace(entry.getKey(), entry.getValue().toString());
        }
        log.debug("Riverbed request payload : {}, branch : {}", template, entity);
        return template;
    }

    public Map<String, Map<String, Object>> buildForensicsRecords(String response, String forensicsType) {
        log.trace("Inside buildForensicsRecords(forensicsType:{}) method", forensicsType);
        Map<String, Map<String, Object>> forensicsRecords = new HashMap<>();

        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(new StringReader(response)));

            NodeList dataNodes = document.getElementsByTagName("data");
            for (int i = 0; i < dataNodes.getLength(); i++) {
                Node dataNode = dataNodes.item(i);
                if (dataNode.getNodeType() == Node.ELEMENT_NODE) {
                    Element dataElement = (Element) dataNode;

                    // Extract session/record identifier
                    String sessionId = getElementValue(dataElement, "session_id", "session_" + i);

                    Map<String, Object> forensicsData = new HashMap<>();

                    // Extract common forensics fields
                    forensicsData.put("source_ip", getElementValue(dataElement, "source_ip", null));
                    forensicsData.put("destination_ip", getElementValue(dataElement, "destination_ip", null));
                    forensicsData.put("source_port", getElementIntValue(dataElement, "source_port"));
                    forensicsData.put("destination_port", getElementIntValue(dataElement, "destination_port"));
                    forensicsData.put("protocol", getElementValue(dataElement, "protocol", null));
                    forensicsData.put("timestamp", getElementLongValue(dataElement, "timestamp"));
                    forensicsData.put("bytes_transferred", getElementLongValue(dataElement, "bytes_transferred"));
                    forensicsData.put("packets_transferred", getElementLongValue(dataElement, "packets_transferred"));
                    forensicsData.put("application_name", getElementValue(dataElement, "application_name", null));
                    forensicsData.put("user_name", getElementValue(dataElement, "user_name", null));
                    forensicsData.put("device_name", getElementValue(dataElement, "device_name", null));
                    forensicsData.put("location", getElementValue(dataElement, "location", null));
                    forensicsData.put("response_time", getElementDoubleValue(dataElement, "response_time"));
                    forensicsData.put("transaction_id", getElementValue(dataElement, "transaction_id", null));
                    forensicsData.put("http_method", getElementValue(dataElement, "http_method", null));
                    forensicsData.put("http_url", getElementValue(dataElement, "http_url", null));
                    forensicsData.put("http_status_code", getElementIntValue(dataElement, "http_status_code"));
                    forensicsData.put("user_agent", getElementValue(dataElement, "user_agent", null));
                    forensicsData.put("referrer", getElementValue(dataElement, "referrer", null));
                    forensicsData.put("severity", getElementValue(dataElement, "severity", null));
                    forensicsData.put("event_type", getElementValue(dataElement, "event_type", null));
                    forensicsData.put("description", getElementValue(dataElement, "description", null));
                    forensicsData.put("is_encrypted", getElementBooleanValue(dataElement, "is_encrypted"));
                    forensicsData.put("encryption_type", getElementValue(dataElement, "encryption_type", null));
                    forensicsData.put("network_latency", getElementDoubleValue(dataElement, "network_latency"));
                    forensicsData.put("server_time", getElementDoubleValue(dataElement, "server_time"));
                    forensicsData.put("error_code", getElementValue(dataElement, "error_code", null));
                    forensicsData.put("error_message", getElementValue(dataElement, "error_message", null));
                    forensicsData.put("domain_instance_id", getElementValue(dataElement, "domain_instance_id", null));
                    forensicsData.put("threat_level", getElementValue(dataElement, "threat_level", null));
                    forensicsData.put("risk_score", getElementValue(dataElement, "risk_score", null));
                    forensicsData.put("analysis_result", getElementValue(dataElement, "analysis_result", null));

                    // Extract custom attributes and metadata
                    forensicsData.put("custom_attributes", extractCustomAttributes(dataElement));
                    forensicsData.put("forensics_metadata", extractForensicsMetadata(dataElement));

                    forensicsRecords.put(sessionId, forensicsData);
                }
            }
        } catch (Exception e) {
            log.error("Error parsing forensics XML response for type: {}", forensicsType, e);
        }

        log.trace("Exiting buildForensicsRecords(forensicsType:{}) method, records count: {}", forensicsType, forensicsRecords.size());
        return forensicsRecords;
    }

    private String getElementValue(Element parent, String tagName, String defaultValue) {
        NodeList nodeList = parent.getElementsByTagName(tagName);
        if (nodeList.getLength() > 0) {
            Node node = nodeList.item(0);
            if (node != null && node.getTextContent() != null) {
                return node.getTextContent().trim();
            }
        }
        return defaultValue;
    }

    private Integer getElementIntValue(Element parent, String tagName) {
        String value = getElementValue(parent, tagName, null);
        if (value != null) {
            try {
                return Integer.valueOf(value);
            } catch (NumberFormatException e) {
                log.debug("Failed to parse integer value for tag: {}, value: {}", tagName, value);
            }
        }
        return null;
    }

    private Long getElementLongValue(Element parent, String tagName) {
        String value = getElementValue(parent, tagName, null);
        if (value != null) {
            try {
                return Long.valueOf(value);
            } catch (NumberFormatException e) {
                log.debug("Failed to parse long value for tag: {}, value: {}", tagName, value);
            }
        }
        return null;
    }

    private Double getElementDoubleValue(Element parent, String tagName) {
        String value = getElementValue(parent, tagName, null);
        if (value != null) {
            try {
                return Double.valueOf(value);
            } catch (NumberFormatException e) {
                log.debug("Failed to parse double value for tag: {}, value: {}", tagName, value);
            }
        }
        return null;
    }

    private Boolean getElementBooleanValue(Element parent, String tagName) {
        String value = getElementValue(parent, tagName, null);
        if (value != null) {
            return Boolean.valueOf(value);
        }
        return null;
    }

    private Map<String, Object> extractCustomAttributes(Element parent) {
        Map<String, Object> customAttributes = new HashMap<>();
        NodeList customNodes = parent.getElementsByTagName("custom_attribute");
        for (int i = 0; i < customNodes.getLength(); i++) {
            Node customNode = customNodes.item(i);
            if (customNode.getNodeType() == Node.ELEMENT_NODE) {
                Element customElement = (Element) customNode;
                String name = customElement.getAttribute("name");
                String value = customElement.getTextContent();
                if (name != null && !name.isEmpty()) {
                    customAttributes.put(name, value);
                }
            }
        }
        return customAttributes;
    }

    private Map<String, String> extractForensicsMetadata(Element parent) {
        Map<String, String> metadata = new HashMap<>();
        NodeList metadataNodes = parent.getElementsByTagName("forensics_metadata");
        for (int i = 0; i < metadataNodes.getLength(); i++) {
            Node metadataNode = metadataNodes.item(i);
            if (metadataNode.getNodeType() == Node.ELEMENT_NODE) {
                Element metadataElement = (Element) metadataNode;
                String key = metadataElement.getAttribute("key");
                String value = metadataElement.getTextContent();
                if (key != null && !key.isEmpty()) {
                    metadata.put(key, value);
                }
            }
        }
        return metadata;
    }

}
