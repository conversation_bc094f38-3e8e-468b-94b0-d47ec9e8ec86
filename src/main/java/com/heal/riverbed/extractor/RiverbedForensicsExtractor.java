package com.heal.riverbed.extractor;

import com.google.common.net.HttpHeaders;
import com.heal.etladapter.beans.DomainEntityTypeKpi;
import com.heal.etladapter.extractors.HttpExtractor;
import com.heal.etladapter.pojos.DomainKpi;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.etladapter.utility.AdapterConstants;
import com.heal.riverbed.constants.Constants;
import com.heal.riverbed.exceptions.RiverbedConnectorException;
import com.heal.riverbed.parser.XmlParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RiverbedForensicsExtractor extends HttpExtractor<Object, List<DomainKpi>> {

    @Autowired
    protected ConnectorKpiMaster connectorKpiMaster;
    protected Map<String, List<DomainEntityTypeKpi>> riverbedForensicsKpisMap;

    @Autowired
    protected XmlParser xmlParser;

    @Override
    public void initialize() throws Exception {
        super.initialize();

        String riverbedUrl = this.parameters.get(Constants.RIVERBED_URL);
        if (riverbedUrl == null || riverbedUrl.trim().isEmpty()) {
            log.error("Riverbed url details unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed url details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        String riverbedForensicsRequest = this.parameters.get(Constants.RIVERBED_FORENSICS_REQUEST);
        if (riverbedForensicsRequest == null || riverbedForensicsRequest.trim().isEmpty()) {
            log.error("Riverbed Forensics request is unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed Forensics request details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        String token = this.parameters.get(Constants.RIVERBED_AUTHORIZATION);
        if (token == null || token.trim().isEmpty()) {
            log.error("Riverbed authorization token is unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed Authorization details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        List<DomainEntityTypeKpi> healForensicsKPIsForEntity = connectorKpiMaster.getHealKPIsForEntity(this.parameters.get(AdapterConstants.DOMAIN), this.jdbcTemplate);
        if (healForensicsKPIsForEntity == null || healForensicsKPIsForEntity.isEmpty()) {
            log.error("Riverbed forensics kpis unavailable in Heal for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed forensics kpis unavailable in Heal for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        String entityTypeNames = this.parameters.get(Constants.RIVERBED_ENTITY_TYPES);
        if (entityTypeNames == null || entityTypeNames.trim().isEmpty()) {
            log.error("Riverbed entity types unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed entity types unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        riverbedForensicsKpisMap = healForensicsKPIsForEntity.stream().collect(Collectors.groupingBy(DomainEntityTypeKpi::getEntityIdentifier));

        if (riverbedForensicsKpisMap.isEmpty()) {
            log.error("Riverbed forensics kpis unavailable for the entity types: {} in Heal for jobId:{}, connector instance:{}. Failing initialization for {}", entityTypeNames, jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed forensics kpis unavailable for the entity types in Heal for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        log.trace("Configured forensics kpi details: {} for jobId:{}, connector instance:{}", riverbedForensicsKpisMap, jobId, connectorInstanceIdentifier);
    }

    @Override
    public List<DomainKpi> extract(long from, long to, Object items) {
        return List.of();
    }

    @Override
    public List<DomainKpi> extract(long from, long to, List<DomainKpi> items) {
        long st = System.currentTimeMillis();
        log.trace("Extracting forensics data from: {} to: {}, jobId:{}, connector instance:{}", from, to, jobId, connectorInstanceIdentifier);
        List<DomainKpi> forensicsKpiList = new ArrayList<>();

        try {
            String intfTypes = this.parameters.getOrDefault("forensics.types", "apt#:1,apt#:2,hos#:1,hos#:2");
            String template = this.parameters.get(Constants.RIVERBED_FORENSICS_REQUEST).trim();
            
            riverbedForensicsKpisMap.forEach((scrInstIdentifier, kpis) ->
                    forensicsKpiList.addAll(Arrays.stream(intfTypes.split(","))
                            .map(String::trim)
                            .map(s -> s.split("#"))
                            .filter(f -> f.length == 2)
                            .map(intfType -> {
                                try {
                                    String groupBy = intfType[0];
                                    String intfName = intfType[1];
                                    String prefix = "forensics_" + groupBy + "_" + intfName;
                                    String limit = this.parameters.getOrDefault(prefix + "_limit", "100");
                                    String resolution = this.parameters.getOrDefault(prefix + "_resolution", "1min");
                                    String columns = this.parameters.getOrDefault(prefix + "_columns", "[6,57,33,34,31,30]");

                                    String payload = xmlParser.prepareRequestPayload(scrInstIdentifier, columns, intfName, groupBy, limit, resolution, from, to, template);

                                    Map<Integer, String> kpiColumns = xmlParser.getKpiColumnIndex(scrInstIdentifier, this.parameters.getOrDefault(prefix + "_kpis", ""));
                                    int attrIndex = Integer.parseInt(this.parameters.getOrDefault(prefix + "_attrIndex", "0"));
                                    return extractAndProcessForensicsKpi(scrInstIdentifier, from, to, payload, kpiColumns, attrIndex, kpis);
                                } catch (Exception e) {
                                    log.error("Error occurred while processing the forensics entity:{}, jobId:{}, connector instance:{}", scrInstIdentifier, jobId, connectorInstanceIdentifier, e);
                                    return null;
                                }
                            }).filter(Objects::nonNull).flatMap(List::stream)
                            .toList()));
        } catch (Exception e) {
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_ERRORS), 1);
            log.error("Error occurred in Forensics Extractor for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }

        log.trace("Forensics kpis fetched for jobId:{}, connector instance:{}, data: {}", jobId, connectorInstanceIdentifier, forensicsKpiList);
        log.info("Time taken to complete riverbed forensics extractor: {}ms, forensics kpi size: {}, jobId:{}, connector instance:{}", (System.currentTimeMillis() - st), forensicsKpiList.size(), jobId, connectorInstanceIdentifier);
        return forensicsKpiList;
    }

    private List<DomainKpi> extractAndProcessForensicsKpi(String scrInstIdentifier, long from, long to, String payload, Map<Integer, String> columnMap, Integer columnAttribute, List<DomainEntityTypeKpi> metrics) {
        try {
            Map<String, DomainEntityTypeKpi> monitorKpiMap = metrics.stream().collect(Collectors.toMap(DomainEntityTypeKpi::getSrcMetricIdentifier, Function.identity(), (a, b) -> a));
            Map<String, String> headers = Map.of(
                    HttpHeaders.CONTENT_TYPE, "application/json",
                    HttpHeaders.AUTHORIZATION, this.parameters.get(Constants.RIVERBED_AUTHORIZATION)
            );
            String riverbedUrl = this.parameters.get(Constants.RIVERBED_URL);
            log.info("Making riverbed forensics api call Url: {}, jobId:{}, connector instance:{}", riverbedUrl, jobId, connectorInstanceIdentifier);
            log.trace("Adding headers for riverbed forensics api call:{}, headers {}, jobId:{}, connector instance:{}", riverbedUrl, headers, jobId, connectorInstanceIdentifier);
            String response = httpConnection.httpPost(riverbedUrl, payload, headers, this.connectorInstanceIdentifier);
            if (response == null || response.isEmpty()) {
                log.error("Invalid response received from Riverbed for forensics url : {}, request : {}, response :{}, jobId:{}, connector instance:{}", riverbedUrl, payload, response, jobId, connectorInstanceIdentifier);
                return Collections.emptyList();
            }

            // Convert the api response to Map with Key is attribute, value map has kpi name and kpi value.
            Map<String, Map<String, Double>> attributeKpiNameData = xmlParser.buildKpiRecords(response, columnAttribute, columnMap, scrInstIdentifier);

            // Convert the map to another map key is kpi name, value map has attribute name and value.
            Map<String, Map<String, Double>> kpiNameAttributesData = xmlParser.pivotMap(attributeKpiNameData, scrInstIdentifier);
            return processForensicsResponse(scrInstIdentifier, kpiNameAttributesData, from, to, monitorKpiMap);
        } catch (Exception e) {
            log.error("Failed processing Forensics Kpi for entity: {}, jobId:{}, connector instance:{}", scrInstIdentifier, jobId, connectorInstanceIdentifier, e);
            return Collections.emptyList();
        }
    }

    private List<DomainKpi> processForensicsResponse(String sourceInstanceId, Map<String, Map<String, Double>> kpiAttributeData, long fromDate, long toDate, Map<String, DomainEntityTypeKpi> monitorKpiMap) {
        log.trace("Inside processForensicsResponse(sourceInstanceId:{}) method", sourceInstanceId);
        List<DomainKpi> domainKpis = new ArrayList<>();
        Date from = new Date(fromDate);
        Date to = new Date(toDate);

        kpiAttributeData.forEach((kpiName, attributeMap) -> {
            DomainEntityTypeKpi kpiDetail = monitorKpiMap.get(kpiName);
            if (kpiDetail == null) {
                log.error("Forensics Kpi details not present in heal for jobId:{}, connector instance:{}, kpiName: {}", jobId, connectorInstanceIdentifier, kpiName);
                return;
            }
            if (!kpiDetail.isGroupKpi()) {
                domainKpis.add(populateForensicsKpiData(sourceInstanceId, attributeMap.get("ALL"), from, to, kpiDetail));
            } else if (kpiDetail.isGroupKpi()) {
                domainKpis.add(populateForensicsGroupKpiData(sourceInstanceId, from, to, attributeMap, kpiDetail));
            } else {
                log.error("Forensics Kpi details are not configured properly, KpiName:{}, from:{}, to:{}, jobId:{}, connector instance:{}", kpiName, from, to, jobId, connectorInstanceIdentifier);
            }
        });

        return domainKpis;
    }

    private DomainKpi populateForensicsKpiData(String srcInstIdentifier, Double kpiValue, Date from, Date to, DomainEntityTypeKpi kpiDetail) {
        try {
            DomainKpi kpi = new DomainKpi();
            kpi.setLowerThreshold(from);
            kpi.setUpperThreshold(to);
            kpi.setKpiName(kpiDetail.getSrcMetricIdentifier());
            kpi.setDomainInstanceId(srcInstIdentifier);
            kpi.setValue(kpiValue);
            kpi.setIsGroupKpi(false);
            log.trace("Populated forensics KPI data: {} for instance: {}", kpi.getKpiName(), srcInstIdentifier);
            return kpi;
        } catch (Exception e) {
            log.error("Exception occurred while extracting the forensics data for the instance: {}, Kpi: {}, for the time: {} to {}, jobId:{}, connector instance:{} ", srcInstIdentifier, kpiDetail, from, to, jobId, connectorInstanceIdentifier, e);
        }
        return null;
    }

    private DomainKpi populateForensicsGroupKpiData(String srcInstIdentifier, Date from, Date to, Map<String, Double> groupKpiAttributes, DomainEntityTypeKpi kpiDetail) {
        try {
            DomainKpi kpi = new DomainKpi();
            kpi.setLowerThreshold(from);
            kpi.setUpperThreshold(to);
            kpi.setKpiUid(kpiDetail.getMstKpiId());
            kpi.setKpiName(kpiDetail.getSrcMetricIdentifier());
            kpi.setDomainInstanceId(srcInstIdentifier);
            kpi.setIsGroupKpi(kpiDetail.isGroupKpi());
            kpi.setKpiGroupName(kpiDetail.getMstGroupName());
            kpi.setGroupName(kpiDetail.getMstGroupName());
            kpi.setGroupKpis(groupKpiAttributes.entrySet().stream()
                    .collect(Collectors
                            .toMap(Map.Entry::getKey,
                                    entry -> String.valueOf(entry.getValue()))));
            log.trace("Populated forensics group KPI data: {} for instance: {}", kpi.getKpiName(), srcInstIdentifier);
            return kpi;
        } catch (Exception e) {
            log.error("Exception occurred while extracting the forensics group data for the instance: {}, Kpi: {}, for the time: {} to {}, jobId:{}, connector instance:{} ", srcInstIdentifier, kpiDetail, from, to, jobId, connectorInstanceIdentifier, e);
        }
        return null;
    }
}
