package com.heal.riverbed.extractor;

import com.google.common.net.HttpHeaders;
import com.heal.etladapter.extractors.HttpExtractor;
import com.heal.riverbed.constants.Constants;
import com.heal.riverbed.exceptions.RiverbedConnectorException;
import com.heal.riverbed.parser.XmlParser;
import com.heal.riverbed.pojos.ForensicsData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RiverbedForensicsExtractor extends HttpExtractor<Object, List<ForensicsData>> {

    @Autowired
    protected XmlParser xmlParser;

    @Override
    public void initialize() throws Exception {
        super.initialize();

        String riverbedUrl = this.parameters.get(Constants.RIVERBED_URL);
        if (riverbedUrl == null || riverbedUrl.trim().isEmpty()) {
            log.error("Riverbed url details unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed url details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        String riverbedForensicsRequest = this.parameters.get(Constants.RIVERBED_FORENSICS_REQUEST);
        if (riverbedForensicsRequest == null || riverbedForensicsRequest.trim().isEmpty()) {
            log.error("Riverbed Forensics request is unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed Forensics request details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        String token = this.parameters.get(Constants.RIVERBED_AUTHORIZATION);
        if (token == null || token.trim().isEmpty()) {
            log.error("Riverbed authorization token is unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed Authorization details unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        String entityTypes = this.parameters.get(Constants.RIVERBED_ENTITY_TYPES);
        if (entityTypes == null || entityTypes.trim().isEmpty()) {
            log.error("Riverbed entity types unavailable in worker parameters for jobId:{}, connector instance:{}. Failing initialization for {}", jobId, connectorInstanceIdentifier, this.className);
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_CONFIGURATION_ERROR), 1);
            throw new RiverbedConnectorException("Riverbed entity types unavailable in worker parameters for jobId:" + jobId + ", connector instance:" + connectorInstanceIdentifier);
        }

        log.trace("Configured forensics extractor for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier);
    }

    @Override
    public List<ForensicsData> extract(long from, long to, Object items) {
        return List.of();
    }

    @Override
    public List<ForensicsData> extract(long from, long to, List<ForensicsData> items) {
        long st = System.currentTimeMillis();
        log.trace("Extracting forensics data from: {} to: {}, jobId:{}, connector instance:{}", from, to, jobId, connectorInstanceIdentifier);
        List<ForensicsData> forensicsDataList = new ArrayList<>();

        try {
            String forensicsTypes = this.parameters.getOrDefault("forensics.types", "network_traffic,security_events,application_flows");
            String template = this.parameters.get(Constants.RIVERBED_FORENSICS_REQUEST).trim();
            
            Arrays.stream(forensicsTypes.split(","))
                    .map(String::trim)
                    .forEach(forensicsType -> {
                        try {
                            String prefix = "forensics_" + forensicsType;
                            String limit = this.parameters.getOrDefault(prefix + "_limit", "1000");
                            String resolution = this.parameters.getOrDefault(prefix + "_resolution", "1min");
                            String filters = this.parameters.getOrDefault(prefix + "_filters", "");

                            List<ForensicsData> typeSpecificData = extractForensicsDataByType(forensicsType, from, to, template, limit, resolution, filters);
                            forensicsDataList.addAll(typeSpecificData);
                        } catch (Exception e) {
                            log.error("Error occurred while processing forensics type:{}, jobId:{}, connector instance:{}", forensicsType, jobId, connectorInstanceIdentifier, e);
                        }
                    });
        } catch (Exception e) {
            healthMetrics.putInExtractorErrors(this.connectorInstanceIdentifier.concat(":").concat(Constants.RIVERBED_FORENSICS_EXTRACTOR_ERRORS), 1);
            log.error("Error occurred in Forensics Extractor for jobId:{}, connector instance:{}", jobId, connectorInstanceIdentifier, e);
        }

        log.trace("Forensics data fetched for jobId:{}, connector instance:{}, data: {}", jobId, connectorInstanceIdentifier, forensicsDataList);
        log.info("Time taken to complete riverbed forensics extractor: {}ms, forensics data size: {}, jobId:{}, connector instance:{}", (System.currentTimeMillis() - st), forensicsDataList.size(), jobId, connectorInstanceIdentifier);
        return forensicsDataList;
    }

    private List<ForensicsData> extractForensicsDataByType(String forensicsType, long from, long to, String template, String limit, String resolution, String filters) {
        try {
            Map<String, String> headers = Map.of(
                    HttpHeaders.CONTENT_TYPE, "application/json",
                    HttpHeaders.AUTHORIZATION, this.parameters.get(Constants.RIVERBED_AUTHORIZATION)
            );

            String payload = prepareForensicsRequestPayload(forensicsType, from, to, template, limit, resolution, filters);
            String riverbedUrl = this.parameters.get(Constants.RIVERBED_URL);
            
            log.info("Making riverbed forensics api call for type: {}, Url: {}, jobId:{}, connector instance:{}", forensicsType, riverbedUrl, jobId, connectorInstanceIdentifier);
            log.trace("Adding headers for riverbed forensics api call:{}, headers {}, jobId:{}, connector instance:{}", riverbedUrl, headers, jobId, connectorInstanceIdentifier);
            
            String response = httpConnection.httpPost(riverbedUrl, payload, headers, this.connectorInstanceIdentifier);
            if (response == null || response.isEmpty()) {
                log.error("Invalid response received from Riverbed for forensics type: {}, url : {}, request : {}, response :{}, jobId:{}, connector instance:{}", forensicsType, riverbedUrl, payload, response, jobId, connectorInstanceIdentifier);
                return Collections.emptyList();
            }

            return parseForensicsResponse(response, forensicsType, from, to);
        } catch (Exception e) {
            log.error("Failed processing Forensics data for type: {}, jobId:{}, connector instance:{}", forensicsType, jobId, connectorInstanceIdentifier, e);
            return Collections.emptyList();
        }
    }

    private String prepareForensicsRequestPayload(String forensicsType, long from, long to, String template, String limit, String resolution, String filters) {
        Date startDate = new Date(from);
        Date endDate = new Date(to);
        
        Map<String, Object> placeholders = Map.of(
                "${startTime}", startDate.toInstant().getEpochSecond(),
                "${endTime}", endDate.toInstant().getEpochSecond(),
                "${resolution}", resolution,
                "${forensicsType}", forensicsType,
                "${limit}", limit,
                "${filters}", filters != null ? filters : ""
        );
        
        String payload = template;
        for (Map.Entry<String, Object> entry : placeholders.entrySet()) {
            payload = payload.replace(entry.getKey(), entry.getValue().toString());
        }
        
        log.debug("Riverbed forensics request payload for type: {}, payload: {}", forensicsType, payload);
        return payload;
    }

    private List<ForensicsData> parseForensicsResponse(String response, String forensicsType, long from, long to) {
        try {
            // Parse XML response and convert to ForensicsData objects
            // This would use the XmlParser to extract forensics-specific data
            Map<String, Map<String, Object>> forensicsRecords = xmlParser.buildForensicsRecords(response, forensicsType);
            
            return forensicsRecords.entrySet().stream()
                    .map(entry -> convertToForensicsData(entry.getKey(), entry.getValue(), forensicsType, from, to))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error parsing forensics response for type: {}, jobId:{}, connector instance:{}", forensicsType, jobId, connectorInstanceIdentifier, e);
            return Collections.emptyList();
        }
    }

    private ForensicsData convertToForensicsData(String sessionId, Map<String, Object> data, String forensicsType, long from, long to) {
        try {
            return ForensicsData.builder()
                    .sessionId(sessionId)
                    .sourceIp(getStringValue(data, "source_ip"))
                    .destinationIp(getStringValue(data, "destination_ip"))
                    .sourcePort(getIntegerValue(data, "source_port"))
                    .destinationPort(getIntegerValue(data, "destination_port"))
                    .protocol(getStringValue(data, "protocol"))
                    .timestamp(new Date(getLongValue(data, "timestamp", from)))
                    .bytesTransferred(getLongValue(data, "bytes_transferred", 0L))
                    .packetsTransferred(getLongValue(data, "packets_transferred", 0L))
                    .applicationName(getStringValue(data, "application_name"))
                    .userName(getStringValue(data, "user_name"))
                    .deviceName(getStringValue(data, "device_name"))
                    .location(getStringValue(data, "location"))
                    .responseTime(getDoubleValue(data, "response_time"))
                    .transactionId(getStringValue(data, "transaction_id"))
                    .httpMethod(getStringValue(data, "http_method"))
                    .httpUrl(getStringValue(data, "http_url"))
                    .httpStatusCode(getIntegerValue(data, "http_status_code"))
                    .userAgent(getStringValue(data, "user_agent"))
                    .referrer(getStringValue(data, "referrer"))
                    .severity(getStringValue(data, "severity"))
                    .eventType(getStringValue(data, "event_type"))
                    .description(getStringValue(data, "description"))
                    .isEncrypted(getBooleanValue(data, "is_encrypted"))
                    .encryptionType(getStringValue(data, "encryption_type"))
                    .networkLatency(getDoubleValue(data, "network_latency"))
                    .serverTime(getDoubleValue(data, "server_time"))
                    .errorCode(getStringValue(data, "error_code"))
                    .errorMessage(getStringValue(data, "error_message"))
                    .domainInstanceId(getStringValue(data, "domain_instance_id"))
                    .forensicsCategory(forensicsType)
                    .threatLevel(getStringValue(data, "threat_level"))
                    .riskScore(getStringValue(data, "risk_score"))
                    .analysisResult(getStringValue(data, "analysis_result"))
                    .customAttributes(getMapValue(data, "custom_attributes"))
                    .forensicsMetadata(getStringMapValue(data, "forensics_metadata"))
                    .build();
        } catch (Exception e) {
            log.error("Error converting data to ForensicsData for session: {}, type: {}, jobId:{}, connector instance:{}", sessionId, forensicsType, jobId, connectorInstanceIdentifier, e);
            return null;
        }
    }

    // Helper methods for safe type conversion
    private String getStringValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        return value != null ? value.toString() : null;
    }

    private Integer getIntegerValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) return null;
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Long getLongValue(Map<String, Object> data, String key, Long defaultValue) {
        Object value = data.get(key);
        if (value == null) return defaultValue;
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    private Double getDoubleValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) return null;
        try {
            return Double.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Boolean getBooleanValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value == null) return null;
        return Boolean.valueOf(value.toString());
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> getMapValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value instanceof Map) {
            return (Map<String, Object>) value;
        }
        return new HashMap<>();
    }

    @SuppressWarnings("unchecked")
    private Map<String, String> getStringMapValue(Map<String, Object> data, String key) {
        Object value = data.get(key);
        if (value instanceof Map) {
            Map<String, String> result = new HashMap<>();
            ((Map<?, ?>) value).forEach((k, v) -> {
                if (k != null && v != null) {
                    result.put(k.toString(), v.toString());
                }
            });
            return result;
        }
        return new HashMap<>();
    }
}
