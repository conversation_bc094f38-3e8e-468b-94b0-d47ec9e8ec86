package com.heal.riverbed.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ForensicsData {
    
    private String sessionId;
    private String sourceIp;
    private String destinationIp;
    private Integer sourcePort;
    private Integer destinationPort;
    private String protocol;
    private Date timestamp;
    private Long bytesTransferred;
    private Long packetsTransferred;
    private String applicationName;
    private String userName;
    private String deviceName;
    private String location;
    private Double responseTime;
    private String transactionId;
    private String httpMethod;
    private String httpUrl;
    private Integer httpStatusCode;
    private String userAgent;
    private String referrer;
    private Map<String, Object> customAttributes;
    private String severity;
    private String eventType;
    private String description;
    private Boolean isEncrypted;
    private String encryptionType;
    private Double networkLatency;
    private Double serverTime;
    private String errorCode;
    private String errorMessage;
    private String domainInstanceId;
    
    // Forensics-specific fields
    private String forensicsCategory;
    private String threatLevel;
    private String riskScore;
    private String analysisResult;
    private Map<String, String> forensicsMetadata;
}
