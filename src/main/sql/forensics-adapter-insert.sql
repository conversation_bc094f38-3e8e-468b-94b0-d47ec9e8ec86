# Forensics Extractor Configuration
# This file contains SQL statements to configure the Riverbed Forensics Extractor

# Insert connector worker for Forensics Extractor
INSERT INTO appsone.connector_workers (`class_path`, `worker_type_id`) 
VALUES ('com.heal.riverbed.extractor.RiverbedForensicsExtractor', (select id from mst_sub_type where name='Extractor'));

# Insert connector worker for Forensics Transformer (using existing HealKPITransformer)
INSERT INTO appsone.connector_workers (`class_path`, `worker_type_id`) 
VALUES ('com.heal.etladapter.transformers.HealKPITransformer', (select id from mst_sub_type where name='Transformer'));

# Insert connector worker for Forensics Loader (using existing HealKPIHttpLoader)
INSERT INTO appsone.connector_workers (`class_path`, `worker_type_id`) 
VALUES ('com.heal.etladapter.loaders.HealKPIHttpLoader', (select id from mst_sub_type where name='Loader'));

# Create connector chain for Forensics
INSERT INTO appsone.connector_chains (`chain_identifier`, `status`, `created_time`, `updated_time`, `user_details_id`) 
VALUES ('Riverbed_Forensics', 1, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

# Map workers to the Forensics chain
INSERT INTO appsone.connector_chain_worker_mapping (`connector_chain_id`, `connector_worker_id`, `status`, `re_initialize`, `order`, `created_time`, `updated_time`, `user_details_id`) 
VALUES ((select id from appsone.connector_chains where chain_identifier='Riverbed_Forensics'), 
        (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'), 
        1, 1, 1, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO appsone.connector_chain_worker_mapping (`connector_chain_id`, `connector_worker_id`, `status`, `re_initialize`, `order`, `created_time`, `updated_time`, `user_details_id`) 
VALUES ((select id from appsone.connector_chains where chain_identifier='Riverbed_Forensics'), 
        (select id from appsone.connector_workers where class_path='com.heal.etladapter.transformers.HealKPITransformer'), 
        1, 1, 2, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

INSERT INTO appsone.connector_chain_worker_mapping (`connector_chain_id`, `connector_worker_id`, `status`, `re_initialize`, `order`, `created_time`, `updated_time`, `user_details_id`) 
VALUES ((select id from appsone.connector_chains where chain_identifier='Riverbed_Forensics'), 
        (select id from appsone.connector_workers where class_path='com.heal.etladapter.loaders.HealKPIHttpLoader'), 
        1, 1, 3, '2025-03-13 00:00:00', '2025-03-13 00:00:00', '7640123a-fbde-4fe5-9812-581cd1e3a9c1');

# Configure parameters for Forensics Extractor
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('domain','riverbed',(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('riverbed.forensics.request.payload', 
'{"criteria":{"time_frame":{"start":${startTime},"end":${endTime},"resolution":"${resolution}"},"queries":[{"sort_column":33,"realm":"traffic_summary","traffic_expression":"interface ${branchIp}${interface}","group_by":"${groupBy}","limit":${limit},"columns":${columns}}]},"template_id":184}',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('riverbed.url','https://riverbed-api-endpoint.com/api/reports',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('riverbed.authorization','Bearer your-auth-token-here',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('riverbed.entity.types','HOST,INTERFACE',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('forensics.types','apt#:1,apt#:2,hos#:1,hos#:2',
(select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

# Forensics-specific interface configurations
INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_apt_:1_limit', '100', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_apt_:1_resolution', '1min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_apt_:1_columns', '[6,57,33,34,31,30]', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_apt_:1_kpis', '57:ResponseTime,33:Throughput,34:PacketLoss,31:Latency,30:Jitter', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_apt_:1_attrIndex', '6', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_apt_:2_limit', '100', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_apt_:2_resolution', '1min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_apt_:2_columns', '[6,57,33,34,31,30]', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_apt_:2_kpis', '57:ResponseTime,33:Throughput,34:PacketLoss,31:Latency,30:Jitter', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_apt_:2_attrIndex', '6', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_hos_:1_limit', '100', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_hos_:1_resolution', '1min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_hos_:1_columns', '[6,57,33,34,31,30]', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_hos_:1_kpis', '57:ResponseTime,33:Throughput,34:PacketLoss,31:Latency,30:Jitter', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_hos_:1_attrIndex', '6', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_hos_:2_limit', '100', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_hos_:2_resolution', '1min', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_hos_:2_columns', '[6,57,33,34,31,30]', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_hos_:2_kpis', '57:ResponseTime,33:Throughput,34:PacketLoss,31:Latency,30:Jitter', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

INSERT INTO `appsone`.`connector_worker_parameters` (`name`, `value`, `connector_worker_id`) 
VALUES ('forensics_hos_:2_attrIndex', '6', (select id from appsone.connector_workers where class_path='com.heal.riverbed.extractor.RiverbedForensicsExtractor'));

# Configure parameters for Forensics Transformer (reusing HealKPITransformer)
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('domain','riverbed',(select id from appsone.connector_workers where class_path='com.heal.etladapter.transformers.HealKPITransformer' and id not in (select connector_worker_id from appsone.connector_chain_worker_mapping where connector_chain_id = (select id from appsone.connector_chains where chain_identifier='Riverbed_KPIs'))));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('default.instance','DEFAULT-FORENSICS-INSTANCE',(select id from appsone.connector_workers where class_path='com.heal.etladapter.transformers.HealKPITransformer' and id not in (select connector_worker_id from appsone.connector_chain_worker_mapping where connector_chain_id = (select id from appsone.connector_chains where chain_identifier='Riverbed_KPIs'))));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('default.agent','DEFAULT-FORENSICS-AGENT',(select id from appsone.connector_workers where class_path='com.heal.etladapter.transformers.HealKPITransformer' and id not in (select connector_worker_id from appsone.connector_chain_worker_mapping where connector_chain_id = (select id from appsone.connector_chains where chain_identifier='Riverbed_KPIs'))));

# Configure parameters for Forensics Loader (reusing HealKPIHttpLoader)
insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('data.receiver.kpi.endpoint','https://haproxy.appnomic:9998/raw-agents-forensics-data',(select id from appsone.connector_workers where class_path='com.heal.etladapter.loaders.HealKPIHttpLoader' and id not in (select connector_worker_id from appsone.connector_chain_worker_mapping where connector_chain_id = (select id from appsone.connector_chains where chain_identifier='Riverbed_KPIs'))));

insert into appsone.connector_worker_parameters (`name`, `value`, `connector_worker_id`)  
values('domain','riverbed',(select id from appsone.connector_workers where class_path='com.heal.etladapter.loaders.HealKPIHttpLoader' and id not in (select connector_worker_id from appsone.connector_chain_worker_mapping where connector_chain_id = (select id from appsone.connector_chains where chain_identifier='Riverbed_KPIs'))));
