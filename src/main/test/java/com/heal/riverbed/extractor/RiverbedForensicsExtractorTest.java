package com.heal.riverbed.extractor;

import com.heal.etladapter.beans.DomainEntityTypeKpi;
import com.heal.etladapter.pojos.DomainKpi;
import com.heal.etladapter.repo.mysql.ConnectorKpiMaster;
import com.heal.riverbed.constants.Constants;
import com.heal.riverbed.exceptions.RiverbedConnectorException;
import com.heal.riverbed.parser.XmlParser;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RiverbedForensicsExtractorTest {

    @InjectMocks
    private RiverbedForensicsExtractor riverbedForensicsExtractor;

    @Mock
    private ConnectorKpiMaster connectorKpiMaster;

    @Mock
    private XmlParser xmlParser;

    @Mock
    private JdbcTemplate jdbcTemplate;

    private Map<String, String> parameters;
    private List<DomainEntityTypeKpi> mockKpis;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // Setup test parameters
        parameters = new HashMap<>();
        parameters.put(Constants.RIVERBED_URL, "https://test-riverbed-url.com/api");
        parameters.put(Constants.RIVERBED_FORENSICS_REQUEST, "{\"test\":\"forensics_payload\"}");
        parameters.put(Constants.RIVERBED_AUTHORIZATION, "Bearer test-token");
        parameters.put(Constants.RIVERBED_ENTITY_TYPES, "HOST,INTERFACE");
        parameters.put("domain", "riverbed");
        parameters.put("forensics.types", "apt#:1,hos#:1");
        parameters.put("forensics_apt_:1_limit", "100");
        parameters.put("forensics_apt_:1_resolution", "1min");
        parameters.put("forensics_apt_:1_columns", "[6,57,33,34]");
        parameters.put("forensics_apt_:1_kpis", "57:ResponseTime,33:Throughput");
        parameters.put("forensics_apt_:1_attrIndex", "6");

        // Setup mock KPIs
        mockKpis = new ArrayList<>();
        DomainEntityTypeKpi kpi1 = new DomainEntityTypeKpi();
        kpi1.setSrcMetricIdentifier("ResponseTime");
        kpi1.setEntityIdentifier("test-entity-1");
        kpi1.setGroupKpi(false);
        mockKpis.add(kpi1);

        DomainEntityTypeKpi kpi2 = new DomainEntityTypeKpi();
        kpi2.setSrcMetricIdentifier("Throughput");
        kpi2.setEntityIdentifier("test-entity-1");
        kpi2.setGroupKpi(true);
        kpi2.setMstGroupName("NetworkMetrics");
        mockKpis.add(kpi2);

        // Set up the extractor with test data
        riverbedForensicsExtractor.parameters = parameters;
        riverbedForensicsExtractor.jdbcTemplate = jdbcTemplate;
        riverbedForensicsExtractor.jobId = "test-job-123";
        riverbedForensicsExtractor.connectorInstanceIdentifier = "test-instance";
        riverbedForensicsExtractor.className = "RiverbedForensicsExtractor";
    }

    @Test
    public void testInitializeSuccess() throws Exception {
        // Mock the KPI master response
        when(connectorKpiMaster.getHealKPIsForEntity(eq("riverbed"), any(JdbcTemplate.class)))
                .thenReturn(mockKpis);

        // Test initialization
        riverbedForensicsExtractor.initialize();

        // Verify that the KPI map is populated
        assertNotNull(riverbedForensicsExtractor.riverbedForensicsKpisMap);
        assertFalse(riverbedForensicsExtractor.riverbedForensicsKpisMap.isEmpty());
        assertTrue(riverbedForensicsExtractor.riverbedForensicsKpisMap.containsKey("test-entity-1"));
    }

    @Test(expected = RiverbedConnectorException.class)
    public void testInitializeFailsWithMissingUrl() throws Exception {
        // Remove URL parameter
        parameters.remove(Constants.RIVERBED_URL);
        riverbedForensicsExtractor.parameters = parameters;

        // Test initialization - should throw exception
        riverbedForensicsExtractor.initialize();
    }

    @Test(expected = RiverbedConnectorException.class)
    public void testInitializeFailsWithMissingForensicsRequest() throws Exception {
        // Remove forensics request parameter
        parameters.remove(Constants.RIVERBED_FORENSICS_REQUEST);
        riverbedForensicsExtractor.parameters = parameters;

        // Test initialization - should throw exception
        riverbedForensicsExtractor.initialize();
    }

    @Test(expected = RiverbedConnectorException.class)
    public void testInitializeFailsWithMissingAuthorization() throws Exception {
        // Remove authorization parameter
        parameters.remove(Constants.RIVERBED_AUTHORIZATION);
        riverbedForensicsExtractor.parameters = parameters;

        // Test initialization - should throw exception
        riverbedForensicsExtractor.initialize();
    }

    @Test(expected = RiverbedConnectorException.class)
    public void testInitializeFailsWithEmptyKpis() throws Exception {
        // Mock empty KPI list
        when(connectorKpiMaster.getHealKPIsForEntity(eq("riverbed"), any(JdbcTemplate.class)))
                .thenReturn(Collections.emptyList());

        // Test initialization - should throw exception
        riverbedForensicsExtractor.initialize();
    }

    @Test
    public void testExtractWithObjectParameter() {
        // Test the extract method with Object parameter
        List<DomainKpi> result = riverbedForensicsExtractor.extract(1000L, 2000L, new Object());
        
        // Should return empty list as per implementation
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractWithEmptyKpiList() throws Exception {
        // Initialize the extractor first
        when(connectorKpiMaster.getHealKPIsForEntity(eq("riverbed"), any(JdbcTemplate.class)))
                .thenReturn(mockKpis);
        riverbedForensicsExtractor.initialize();

        // Test extract with empty input list
        List<DomainKpi> result = riverbedForensicsExtractor.extract(1000L, 2000L, Collections.emptyList());
        
        // Should return empty list
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testExtractHandlesException() throws Exception {
        // Initialize the extractor first
        when(connectorKpiMaster.getHealKPIsForEntity(eq("riverbed"), any(JdbcTemplate.class)))
                .thenReturn(mockKpis);
        riverbedForensicsExtractor.initialize();

        // Mock XML parser to throw exception
        when(xmlParser.prepareRequestPayload(anyString(), anyString(), anyString(), anyString(), 
                anyString(), anyString(), anyLong(), anyLong(), anyString()))
                .thenThrow(new RuntimeException("Test exception"));

        // Test extract - should handle exception gracefully
        List<DomainKpi> result = riverbedForensicsExtractor.extract(1000L, 2000L, Collections.emptyList());
        
        // Should return empty list when exception occurs
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testParameterDefaults() throws Exception {
        // Initialize the extractor first
        when(connectorKpiMaster.getHealKPIsForEntity(eq("riverbed"), any(JdbcTemplate.class)))
                .thenReturn(mockKpis);
        riverbedForensicsExtractor.initialize();

        // Remove some optional parameters to test defaults
        parameters.remove("forensics.types");
        parameters.remove("forensics_apt_:1_limit");
        riverbedForensicsExtractor.parameters = parameters;

        // Mock XML parser
        when(xmlParser.prepareRequestPayload(anyString(), anyString(), anyString(), anyString(), 
                anyString(), anyString(), anyLong(), anyLong(), anyString()))
                .thenReturn("test-payload");
        when(xmlParser.getKpiColumnIndex(anyString(), anyString()))
                .thenReturn(new HashMap<>());

        // Test extract - should use default values
        List<DomainKpi> result = riverbedForensicsExtractor.extract(1000L, 2000L, Collections.emptyList());
        
        // Should not fail and return a list
        assertNotNull(result);
    }
}
