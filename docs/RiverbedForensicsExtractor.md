# Riverbed Forensics Extractor

## Overview

The `RiverbedForensicsExtractor` is a specialized data extraction component designed to collect forensics data from Riverbed network monitoring systems. It follows the same architectural pattern as the existing `RiverbedKpisExtractor` but is specifically tailored for forensics data collection and analysis.

## Architecture

The extractor extends `HttpExtractor<Object, List<DomainKpi>>` and integrates with the Heal ETL adapter framework to:

1. **Extract** forensics data from Riverbed APIs
2. **Transform** the data using the existing HealKPITransformer
3. **Load** the data using the existing HealKPIHttpLoader

## Key Features

- **HTTP-based data extraction** from Riverbed forensics APIs
- **Configurable interface types** and metrics collection
- **XML response parsing** using the existing XmlParser
- **Error handling and logging** with comprehensive monitoring
- **Health metrics integration** for operational monitoring
- **Flexible parameter configuration** for different forensics scenarios

## Configuration Parameters

### Required Parameters

| Parameter | Description | Example |
|-----------|-------------|---------|
| `riverbed.url` | Riverbed API endpoint URL | `https://riverbed-api.com/api/reports` |
| `riverbed.forensics.request.payload` | JSON template for forensics requests | See payload template below |
| `riverbed.authorization` | Authorization token | `Bearer your-token-here` |
| `riverbed.entity.types` | Entity types to monitor | `HOST,INTERFACE` |
| `domain` | Domain identifier | `riverbed` |

### Optional Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `forensics.types` | Interface types to process | `apt#:1,apt#:2,hos#:1,hos#:2` |
| `forensics_{type}_limit` | Result limit per interface type | `100` |
| `forensics_{type}_resolution` | Time resolution | `1min` |
| `forensics_{type}_columns` | Column indices to extract | `[6,57,33,34,31,30]` |
| `forensics_{type}_kpis` | KPI mappings | `57:ResponseTime,33:Throughput...` |
| `forensics_{type}_attrIndex` | Attribute column index | `0` |

## Payload Template

The forensics request payload template supports the following placeholders:

```json
{
  "criteria": {
    "time_frame": {
      "start": "${startTime}",
      "end": "${endTime}",
      "resolution": "${resolution}"
    },
    "queries": [{
      "sort_column": 33,
      "realm": "traffic_summary",
      "traffic_expression": "interface ${branchIp}${interface}",
      "group_by": "${groupBy}",
      "limit": ${limit},
      "columns": ${columns}
    }]
  },
  "template_id": 184
}
```

### Placeholder Variables

- `${startTime}` - Unix timestamp for query start time
- `${endTime}` - Unix timestamp for query end time
- `${resolution}` - Time resolution (e.g., "1min", "5min")
- `${branchIp}` - Entity identifier (branch IP)
- `${interface}` - Interface identifier
- `${groupBy}` - Grouping criteria
- `${limit}` - Maximum number of results
- `${columns}` - JSON array of column indices

## Interface Types

The extractor supports multiple interface types with the format `{groupBy}#{interface}`:

- `apt#:1` - Application interface type 1
- `apt#:2` - Application interface type 2
- `hos#:1` - Host interface type 1
- `hos#:2` - Host interface type 2

Each interface type can have its own configuration parameters.

## KPI Mapping

KPIs are mapped using column indices to metric names:

```
57:ResponseTime,33:Throughput,34:PacketLoss,31:Latency,30:Jitter
```

## Data Flow

1. **Initialization**: Validates configuration and loads KPI mappings from database
2. **Extraction**: For each configured interface type:
   - Prepares API request payload
   - Makes HTTP POST request to Riverbed API
   - Parses XML response
   - Converts to DomainKpi objects
3. **Processing**: Handles both individual and group KPIs
4. **Error Handling**: Logs errors and updates health metrics

## Error Handling

The extractor includes comprehensive error handling:

- **Configuration validation** during initialization
- **API response validation** during extraction
- **Health metrics tracking** for monitoring
- **Graceful degradation** when individual entities fail

## Health Metrics

The following health metrics are tracked:

- `RiverbedForensicsExtractorConfigurationError` - Configuration issues
- `RiverbedForensicsExtractorErrors` - Runtime extraction errors
- `RiverbedForensicsExtractorInitializationError` - Initialization failures

## Database Configuration

Use the provided SQL script `forensics-adapter-insert.sql` to configure:

1. Connector workers for extractor, transformer, and loader
2. Connector chain for forensics processing
3. Worker parameters for configuration
4. Chain mappings for proper execution order

## Usage Example

```java
// The extractor is automatically instantiated by the Spring framework
// Configuration is loaded from database parameters
// Execution is triggered by the ETL framework

// Example of manual usage (for testing):
RiverbedForensicsExtractor extractor = new RiverbedForensicsExtractor();
extractor.initialize(); // Loads configuration
List<DomainKpi> forensicsData = extractor.extract(startTime, endTime, inputData);
```

## Testing

The `RiverbedForensicsExtractorTest` class provides comprehensive unit tests covering:

- Successful initialization
- Configuration validation
- Error handling scenarios
- Parameter defaults
- Data extraction flows

Run tests using:
```bash
mvn test -Dtest=RiverbedForensicsExtractorTest
```

## Monitoring and Logging

The extractor provides detailed logging at multiple levels:

- **TRACE**: Detailed execution flow and data
- **DEBUG**: Configuration and processing details
- **INFO**: High-level operation status
- **ERROR**: Failures and exceptions

Logs are written to the configured log files with proper correlation IDs for tracking.

## Integration

The forensics extractor integrates seamlessly with the existing Heal ETL framework:

- Uses existing `XmlParser` for response processing
- Leverages `ConnectorKpiMaster` for KPI configuration
- Integrates with health metrics system
- Follows established logging patterns

## Performance Considerations

- **Configurable limits** prevent excessive data retrieval
- **Parallel processing** of multiple interface types
- **Connection pooling** for HTTP requests
- **Memory-efficient** streaming where possible

## Security

- **Token-based authentication** for API access
- **Secure parameter storage** in database
- **Input validation** for all configuration parameters
- **Error message sanitization** to prevent information leakage
